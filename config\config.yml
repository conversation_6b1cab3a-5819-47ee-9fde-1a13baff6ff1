# 服务器配置
server:
  host: "0.0.0.0"  # 监听所有接口，适合宝塔部署
  port: 7768

# 数据库配置 - TiDB Cloud数据库（支持TLS安全连接）
database:
  host: "gateway01.ap-southeast-1.prod.aws.tidbcloud.com"  # TiDB Cloud网关地址，请替换为您的实际地址
  port: 4000  # TiDB Cloud默认端口为4000
  user: "your_tidb_username"  # 请修改为您的TiDB Cloud用户名
  password: "your_tidb_password"  # 请修改为您的TiDB Cloud密码
  name: "your_database_name"  # 数据库名称

  # TLS/SSL安全连接配置（TiDB Cloud要求）
  ssl_enabled: true  # 启用SSL/TLS连接（TiDB Cloud必须启用）
  ssl_verify_cert: true  # 验证服务器证书（推荐开启以提高安全性）
  ssl_ca_path: null  # TiDB Cloud使用公共CA，通常不需要自定义CA证书
  ssl_cert_path: null  # TiDB Cloud单向认证，不需要客户端证书
  ssl_key_path: null  # TiDB Cloud单向认证，不需要客户端私钥

# JWT (JSON Web Token) 配置
jwt:
  secret_key: "oyrgqpig680t87gFOUVPWBPF6587IUF8futcfytdiyd64e" # 请务必修改为一个复杂且唯一的密钥, 建议通过环境变量覆盖
  algorithm: "HS256"
  access_token_expire_minutes: 1440 # 令牌有效期（分钟），例如 1天
